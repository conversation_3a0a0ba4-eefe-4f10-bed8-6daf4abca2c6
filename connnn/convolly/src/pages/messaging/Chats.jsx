import React, { useEffect, useState } from "react";
import { getUser, getUserConversations } from "./utils/api";
import { useParams, useSearchParams } from "react-router-dom";
import { decryptText } from "./utils/crypto";
import ChatRoom from "./components/ChatRoom";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import dayjs from "dayjs";
import userVector from "@/assets/svgs/userVector.svg";

export const CONVERSATION_PARAM_KEY = "chat_cid";

const Chats = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [chats, setChats] = useState([]);
  const [conversationSelected, setConversationSelected] = useState(false);
  const currentUser = useSelector((state) => state?.app?.userInfo?.user);
  const accessToken = useSelector((state) => state?.app?.userInfo?.accessToken);
  const [otherUser, setOtherUser] = useState(null);
  const [fetching, setFetching] = useState(true);
  const [initialMessage, setInitialMessage] = useState("");

  const conversationId = searchParams.get(CONVERSATION_PARAM_KEY) || "";

  // Check for initial message from URL params (when coming from tutor card)
  useEffect(() => {
    const messageParam = searchParams.get("message");
    if (messageParam) {
      setInitialMessage(decodeURIComponent(messageParam));
      // Remove the message param from URL to clean it up
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete("message");
      navigate(`${window.location.pathname}?${newSearchParams.toString()}`, { replace: true });
    }
  }, [searchParams, navigate]);

  useEffect(() => {
    const setup = async () => {
      try {
        const user = await getUser({ userId });
        setOtherUser(user);
      } catch (error) {
        console.error("Error fetching user:", error);
      }
    };
    if (userId) {
      setup();
    }
  }, [userId]);

  useEffect(() => {
    const setup = async () => {
      if (!currentUser || !otherUser) return;

      try {
        const data = await getUserConversations(currentUser.id, accessToken);
        let chats = [];

        for (const chat of data.conversations || data || []) {
          try {
            const text = chat.lastMessage ? await decryptText(currentUser, chat.lastMessage) : "Start a new conversation";
            chats.push({
              ...chat,
              text,
              lastMessageDate: chat.lastMessage?.createdAt || chat.updatedAt
            });
          } catch (err) {
            console.error("Error decrypting message:", err);
            chats.push({
              ...chat,
              text: "Start a new conversation",
              lastMessageDate: chat.lastMessage?.createdAt || chat.updatedAt
            });
          }
        }

        if (!chats.find((chat) => !!chat.participants[otherUser.id])) {
          chats = [
            {
              participants: { [otherUser.id]: otherUser },
              text: "Start a new conversation",
              lastMessageDate: new Date().toISOString()
            },
            ...chats
          ];
          setConversationSelected(true);
        }

        setChats(chats);
        setFetching(false);
      } catch (err) {
        console.error("Error fetching conversations:", err);
        setFetching(false);
      }
    };

    setup();
  }, [currentUser, otherUser, accessToken]);

  const handleChatClick = (chat) => {
    if (!chat.id) return;

    // Find the other participant (not current user)
    const otherParticipantId = Object.keys(chat.participants || {}).find(
      (id) => id !== currentUser?.id
    );

    if (!otherParticipantId) return;

    // the route can be tutor or student
    navigate(
      `/${currentUser?.role}/messages/${otherParticipantId}?${CONVERSATION_PARAM_KEY}=${chat.id}`
    );
  };

  return (
    <div className="w-full h-full">
      <div className="w-full sm:flex h-full gap-5">
        <div className="w-full border rounded-md bg-gray-50 overflow-hidden sm:max-w-[40%]">
          <div className="overflow-y-auto h-full">
            {fetching ? (
              <p className="text-gray-500 text-center p-4">
                Loading conversations...
              </p>
            ) : chats.length > 0 ? (
              chats.map((chat, i) => {
                // Get all participant IDs except current user
                const participantIds = Object.keys(
                  chat?.participants || {}
                ).filter((id) => id !== currentUser?.id);

                // Get the first other participant
                const otherParticipant =
                  participantIds.length > 0
                    ? chat.participants[participantIds[0]]
                    : null;

                return (
                  <div
                    key={i}
                    onClick={() => handleChatClick(chat)}
                    className={`p-3 cursor-pointer hover:bg-gray-100 flex items-center gap-3 border-b ${
                      chat.id === conversationId ? "bg-[#EBEDF0]" : "bg-white"
                    }`}
                  >
                    {otherParticipant?.image && (
                      <img
                        src={otherParticipant.image || userVector}
                        alt={otherParticipant.firstname}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    )}

                    <div className="w-full">
                      <div className="flex justify-between items-center">
                        <p className="font-medium truncate">
                          {otherParticipant
                            ? `${otherParticipant.firstname} ${otherParticipant.lastname}`
                            : "Unknown User"}
                        </p>

                        <span className="text-xs text-gray-500 whitespace-nowrap">
                          {dayjs(chat.lastMessageDate).format("MMM D")}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500 truncate">
                        {chat.text}
                      </p>
                    </div>
                  </div>
                );
              })
            ) : (
              <p className="text-gray-500 text-center p-4">
                Start a conversation with someone
              </p>
            )}
          </div>
        </div>

        <div className="w-full sm:max-w-[60%]">
          <ChatRoom
            selected={conversationSelected}
            userId={userId}
            otherUser={otherUser}
            initialMessage={initialMessage}
          />
        </div>
      </div>
    </div>
  );
};

export default Chats;
