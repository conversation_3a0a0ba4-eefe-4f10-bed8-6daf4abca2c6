import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useChat } from '../../../hooks/useChat';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { 
  FiSearch, 
  FiMoreVertical, 
  FiMessageCircle, 
  FiUsers,
  FiRefreshCw,
  FiX 
} from 'react-icons/fi';
import userVector from '@/assets/svgs/userVector.svg';

dayjs.extend(relativeTime);

/**
 * Enhanced conversation list component with search, filtering, and real-time updates
 */
const ConversationList = ({ 
  selectedConversationId, 
  onConversationSelect,
  className = '',
  showHeader = true,
  searchable = true,
}) => {
  const navigate = useNavigate();
  const currentUser = useSelector(state => state.app?.userInfo?.user);
  
  const {
    conversations,
    conversationsLoading,
    conversationsError,
    searchQuery,
    searchResults,
    searchLoading,
    unreadByConversation,
    onlineUsers,
    loadConversations,
    searchChats,
    setActiveConversationId,
  } = useChat();

  // Local state
  const [localSearchQuery, setLocalSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Load conversations on mount
  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (localSearchQuery.trim()) {
        setIsSearching(true);
        searchChats(localSearchQuery, { type: 'conversations' })
          .finally(() => setIsSearching(false));
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [localSearchQuery, searchChats]);

  // Determine which conversations to show
  const displayConversations = useMemo(() => {
    if (localSearchQuery.trim() && searchResults.length > 0) {
      return searchResults.conversations || [];
    }
    return conversations;
  }, [localSearchQuery, searchResults, conversations]);

  // Handle conversation click
  const handleConversationClick = (conversation) => {
    if (!conversation.id) return;

    // Find the other participant
    const otherParticipantId = Object.keys(conversation.participants || {}).find(
      id => id !== currentUser?.id
    );

    if (!otherParticipantId) return;

    // Update active conversation
    setActiveConversationId(conversation.id);
    
    // Call parent callback
    if (onConversationSelect) {
      onConversationSelect(conversation);
    }

    // Navigate to conversation
    navigate(
      `/${currentUser?.role}/messages/${otherParticipantId}?chat_cid=${conversation.id}`
    );
  };

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadConversations({ force: true });
    } finally {
      setRefreshing(false);
    }
  };

  // Clear search
  const clearSearch = () => {
    setLocalSearchQuery('');
    setIsSearching(false);
  };

  // Get participant info for display
  const getParticipantInfo = (conversation) => {
    const participantIds = Object.keys(conversation.participants || {}).filter(
      id => id !== currentUser?.id
    );
    
    if (participantIds.length === 0) return null;
    
    const participant = conversation.participants[participantIds[0]];
    return participant;
  };

  // Format last message preview
  const formatLastMessage = (conversation) => {
    if (!conversation.lastMessage) {
      return 'Start a conversation';
    }

    const message = conversation.lastMessage;
    if (message.files && message.files.length > 0) {
      return `📎 ${message.files.length} file(s)`;
    }

    // This would need to be decrypted in a real implementation
    return message.text || 'New message';
  };

  // Get unread count for conversation
  const getUnreadCount = (conversationId) => {
    return unreadByConversation[conversationId] || 0;
  };

  // Check if user is online
  const isUserOnline = (userId) => {
    return onlineUsers[userId] || false;
  };

  if (conversationsError) {
    return (
      <div className={`flex flex-col items-center justify-center p-6 text-center ${className}`}>
        <div className="text-red-500 mb-2">
          <FiMessageCircle size={48} />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Failed to load conversations
        </h3>
        <p className="text-gray-500 mb-4">
          {conversationsError.message || 'Something went wrong'}
        </p>
        <button
          onClick={handleRefresh}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      {showHeader && (
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-xl font-semibold text-gray-900">Messages</h2>
            <div className="flex items-center gap-2">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                title="Refresh conversations"
              >
                <FiRefreshCw 
                  size={18} 
                  className={refreshing ? 'animate-spin' : ''} 
                />
              </button>
              <button className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors">
                <FiMoreVertical size={18} />
              </button>
            </div>
          </div>

          {/* Search */}
          {searchable && (
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                value={localSearchQuery}
                onChange={(e) => setLocalSearchQuery(e.target.value)}
                placeholder="Search conversations..."
                className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary"
              />
              {localSearchQuery && (
                <button
                  onClick={clearSearch}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <FiX className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto">
        {conversationsLoading && displayConversations.length === 0 ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : displayConversations.length > 0 ? (
          <div className="divide-y divide-gray-100">
            {displayConversations.map((conversation, index) => {
              const participant = getParticipantInfo(conversation);
              const unreadCount = getUnreadCount(conversation.id);
              const isSelected = conversation.id === selectedConversationId;
              const isOnline = participant ? isUserOnline(participant._id) : false;

              return (
                <div
                  key={conversation.id || index}
                  onClick={() => handleConversationClick(conversation)}
                  className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                    isSelected ? 'bg-blue-50 border-r-2 border-primary' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {/* Avatar */}
                    <div className="relative flex-shrink-0">
                      <img
                        src={participant?.profilePicture || participant?.image || userVector}
                        alt={participant?.firstName || 'User'}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                      {isOnline && (
                        <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {participant 
                            ? `${participant.firstName || participant.firstname || ''} ${participant.lastName || participant.lastname || ''}`.trim()
                            : 'Unknown User'
                          }
                        </p>
                        <div className="flex items-center space-x-2">
                          {conversation.lastMessage?.createdAt && (
                            <span className="text-xs text-gray-500">
                              {dayjs(conversation.lastMessage.createdAt).fromNow()}
                            </span>
                          )}
                          {unreadCount > 0 && (
                            <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                              {unreadCount > 99 ? '99+' : unreadCount}
                            </span>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-500 truncate mt-1">
                        {formatLastMessage(conversation)}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <div className="text-gray-400 mb-4">
              {localSearchQuery ? <FiSearch size={48} /> : <FiUsers size={48} />}
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {localSearchQuery ? 'No conversations found' : 'No conversations yet'}
            </h3>
            <p className="text-gray-500">
              {localSearchQuery 
                ? 'Try adjusting your search terms'
                : 'Start a conversation with someone to see it here'
              }
            </p>
          </div>
        )}

        {/* Loading indicator for search */}
        {(isSearching || searchLoading) && (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2 text-sm text-gray-500">Searching...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationList;
