import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  useMemo
} from "react";
import { useSearchParams } from "react-router-dom";
// import { useChat } from "../../../hooks/useChat"; // Temporarily disabled
import { useSelector } from "react-redux";
import { decryptText, encryptMessage, encryptFile } from "../utils/crypto";
import { getMessages } from "../utils/api";
import { useSocket } from "@/hooks/useSocket";
import { debounce, removeDuplicate, safelyBind } from "../utils";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import {
  FiSend,
  FiPaperclip,
  FiX,
  FiFile,
  FiImage,
  FiDownload,
  FiMoreVertical,
  FiSmile,
  FiPhone,
  FiVideo,
  FiInfo,
  FiMessageCircle
} from "react-icons/fi";
import userVector from "@/assets/svgs/userVector.svg";

dayjs.extend(relativeTime);

const ChatRoom = ({
  selected,
  userId,
  otherUser,
  className = "",
  showHeader = true,
  showTypingIndicator = true,
  enableFileSharing = true,
  maxFileSize = 10 * 1024 * 1024, // 10MB
}) => {
  // Enhanced state management
  const [chatMessage, setChatMessage] = useState("");
  const [filePreviews, setFilePreviews] = useState([]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [isComposing, setIsComposing] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [messageToReply, setMessageToReply] = useState(null);
  const [showMessageActions, setShowMessageActions] = useState(null);

  // Refs
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const textareaRef = useRef(null);
  const messagesContainerRef = useRef(null);

  // Get conversation ID from URL params
  const conversationId = searchParams.get("chat_cid") || "";

  // Get current user data from Redux store
  const currentUser = useSelector((state) => state?.app?.userInfo?.user);
  const accessToken = useSelector((state) => state?.app?.userInfo?.accessToken);

  // State management
  const [chatMessages, setChatMessages] = useState([]);
  const [typing, setTyping] = useState(null);
  const [fetchingMessages, setFetchingMessages] = useState(true);

  // Refs
  const stateRef = useRef({ isTyping: false });
  const typingTimeoutRef = useRef(null);

  // Socket configuration
  const socketOpts = useMemo(
    () => ({
      auth: {
        token: accessToken
      }
    }),
    [accessToken]
  );

  const socketApi = useSocket(
    "https://convolly-backend.onrender.com",
    currentUser ? socketOpts : undefined
  );

  const { socket, connected } = socketApi;

  // Group messages by date
  const groupedMessages = useMemo(() => {
    const groups = {};
    chatMessages.forEach((message) => {
      const date = dayjs(message.createdAt || new Date()).format("YYYY-MM-DD");
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });
    return groups;
  }, [chatMessages]);

  // Auto-resize textarea based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${Math.min(
        textareaRef.current.scrollHeight,
        120
      )}px`;
    }
  }, [chatMessage]);

  // Clean up object URLs and timeouts when component unmounts
  useEffect(() => {
    return () => {
      filePreviews.forEach((preview) => {
        URL.revokeObjectURL(preview.preview);
      });
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [filePreviews]);

  // Fetch messages when conversation changes
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        if (!currentUser) return;

        setFetchingMessages(true);
        const messages = [];

        const data = conversationId
          ? await getMessages(currentUser.id, conversationId, accessToken)
          : [];

        for (const msg of data.messages || data || []) {
          const text = await decryptText(currentUser, msg);
          messages.push({ ...msg, text });
        }

        setChatMessages(messages);
      } catch (err) {
        console.error("Failed to fetch messages:", err);
        setChatMessages([]);
      } finally {
        setFetchingMessages(false);
      }
    };

    fetchMessages();
  }, [currentUser, conversationId, accessToken]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatMessages]);

  // Update conversation ID in URL
  const updateConversationId = useCallback(
    (newConversationId) => {
      setSearchParams(
        (params) => {
          params.set("chat_cid", newConversationId);
          return params;
        },
        { replace: true }
      );
    },
    [setSearchParams]
  );

  // Mark messages as read when conversation becomes active
  useEffect(() => {
    if (conversationId && chatMessages.length > 0) {
      const unreadMessages = chatMessages.filter(
        msg => msg.sender?.id !== currentUser?.id && !msg.readAt
      );

      if (unreadMessages.length > 0) {
        const messageIds = unreadMessages.map(msg => msg.id).filter(Boolean);
        if (messageIds.length > 0) {
          // TODO: Implement markMessagesAsRead function
          console.log('Mark messages as read:', messageIds);
        }
      }
    }
  }, [conversationId, chatMessages, currentUser?.id]);

  // Load more messages when scrolling to top
  const handleScroll = useCallback(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    if (container.scrollTop === 0) {
      // TODO: Implement load more messages functionality
      console.log('Load more messages for conversation:', conversationId);
    }
  }, [conversationId]);

  // Enhanced file handling
  const handleFileSelect = () => {
    if (!enableFileSharing) return;
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    // Validate files
    const validFiles = files.filter(file => {
      if (file.size > maxFileSize) {
        alert(`File "${file.name}" is too large. Maximum size is ${Math.round(maxFileSize / 1024 / 1024)}MB.`);
        return false;
      }
      return true;
    });

    const newPreviews = validFiles.map((file) => {
      const isImage = file.type.startsWith("image/");
      return {
        file,
        preview: isImage ? URL.createObjectURL(file) : null,
        type: isImage ? "image" : "file",
        name: file.name,
        size: file.size,
        id: `${Date.now()}_${Math.random()}`,
      };
    });

    setFilePreviews((prev) => [...prev, ...newPreviews]);
    e.target.value = ""; // Reset file input
  };

  const removeFilePreview = (index) => {
    const preview = filePreviews[index];
    if (preview.preview) {
      URL.revokeObjectURL(preview.preview);
    }
    setFilePreviews((prev) => prev.filter((_, i) => i !== index));
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Enhanced message sending
  const sendChatMessage = async () => {
    if (!chatMessage.trim() && filePreviews.length === 0) return;
    if (!otherUser || !currentUser) return;

    try {
      // TODO: Implement sendMessage function
      console.log('Sending message:', { conversationId, chatMessage, filePreviews, otherUser });

      // For now, just add the message to local state
      const newMessage = {
        id: Date.now().toString(),
        text: chatMessage,
        sender: currentUser,
        createdAt: new Date().toISOString(),
        tempId: Date.now().toString()
      };

      setChatMessages(prev => [...prev, newMessage]);

      // Reset form
      setChatMessage("");
      setFilePreviews([]);
      setMessageToReply(null);

      // Update conversation ID if this was a new conversation
      if (!conversationId) {
        // The hook will handle updating the URL when the message is saved
      }
    } catch (error) {
      console.error("Error sending message:", error);
      alert("Failed to send message. Please try again.");
    }
  };

  // Enhanced typing handler
  const handleTypingChange = (e) => {
    const value = e.target.value;
    setChatMessage(value);

    // Handle typing indicators
    if (conversationId && otherUser?.id && value.length > 0) {
      // TODO: Implement handleTyping function
      console.log('User is typing in conversation:', conversationId);
    } else if (conversationId && otherUser?.id) {
      // TODO: Implement stopTyping function
      console.log('User stopped typing in conversation:', conversationId);
    }
  };

  // Handle key press events
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
      e.preventDefault();
      sendChatMessage();
    }
  };

  // Handle composition events for better international input support
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const handleCompositionStart = () => setIsComposing(true);
    const handleCompositionEnd = () => setIsComposing(false);

    textarea.addEventListener("compositionstart", handleCompositionStart);
    textarea.addEventListener("compositionend", handleCompositionEnd);

    return () => {
      textarea.removeEventListener("compositionstart", handleCompositionStart);
      textarea.removeEventListener("compositionend", handleCompositionEnd);
    };
  }, []);

  const withConversation = conversationId || selected;

  return (
    <div className={`w-full mx-auto border rounded-md bg-gray-50 h-full flex flex-col ${className}`}>
      {/* Enhanced Header */}
      {showHeader && (
        <div className="border-b bg-white p-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <img
                src={otherUser?.image || otherUser?.profilePicture || userVector}
                alt={otherUser?.firstname || otherUser?.firstName || 'User'}
                className="w-12 h-12 rounded-full object-cover"
              />
              {connected && (
                <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
              )}
            </div>
            <div>
              <h3 className="text-xl font-semibold">
                {otherUser?.firstname || otherUser?.firstName} {otherUser?.lastname || otherUser?.lastName}
              </h3>
              <p className={`text-sm ${connected ? "text-green-500" : "text-gray-500"}`}>
                {connected ? "Online" : "Offline"}
                {!connected && " • Disconnected"}
              </p>
            </div>
          </div>

          {/* Header Actions */}
          <div className="flex items-center gap-2">
            <button
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
              title="Voice call"
            >
              <FiPhone size={18} />
            </button>
            <button
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
              title="Video call"
            >
              <FiVideo size={18} />
            </button>
            <button
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
              title="Conversation info"
            >
              <FiInfo size={18} />
            </button>
            <button
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
              onClick={() => setShowMessageActions(!showMessageActions)}
            >
              <FiMoreVertical size={18} />
            </button>
          </div>
        </div>
      )}

      {/* Enhanced Messages Container */}
      <div className="flex-1 flex flex-col p-4 h-full overflow-hidden">
        <div
          ref={messagesContainerRef}
          onScroll={handleScroll}
          className="flex-1 overflow-y-auto mb-4 p-2 bg-white border rounded-md h-full"
        >
          {/* Loading indicator for fetching messages */}
          {fetchingMessages && (
            <div className="flex justify-center p-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          )}

          {withConversation ? (
            <div className="space-y-4">
              {Object.entries(groupedMessages).map(([date, messages]) => (
                <div key={date} className="space-y-2">
                  <div className="text-center text-xs text-gray-500 my-2">
                    {dayjs(date).format("MMMM D, YYYY")}
                  </div>
                  {messages.map((message) => {
                    const isOwnMessage = message.sender?.id === currentUser?.id;
                    const isPending = message.tempId && !message.id;

                    return (
                      <div
                        key={message.tempId || message.id}
                        className={`flex ${isOwnMessage ? "justify-end" : "justify-start"} mb-2`}
                      >
                        <div
                          className={`max-w-[70%] p-3 rounded-lg relative group ${
                            isOwnMessage
                              ? "bg-primary text-white"
                              : "bg-gray-100 text-gray-900"
                          } ${isPending ? "opacity-70" : ""}`}
                        >
                          {/* Reply indicator */}
                          {message.replyTo && (
                            <div className="text-xs opacity-75 mb-2 p-2 bg-black bg-opacity-10 rounded">
                              Replying to: {message.replyTo.text?.substring(0, 50)}...
                            </div>
                          )}

                          {/* Message text */}
                          {message.text && (
                            <p className="text-sm mb-2 whitespace-pre-wrap">{message.text}</p>
                          )}

                          {/* Enhanced file attachments */}
                          {message.files?.length > 0 && (
                            <div className="flex gap-2 mb-2 flex-wrap">
                              {message.files.map((file, index) => (
                                <div key={index} className="max-w-[200px]">
                                  {file.type === "image" && file.preview ? (
                                    <div className="relative group">
                                      <img
                                        src={file.preview}
                                        alt="Attachment"
                                        className="max-h-40 rounded border cursor-pointer hover:opacity-90"
                                        onClick={() => {/* TODO: Open image viewer */}}
                                      />
                                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded transition-all duration-200 flex items-center justify-center">
                                        <FiImage className="text-white opacity-0 group-hover:opacity-100" size={24} />
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="flex items-center gap-2 p-2 bg-white bg-opacity-20 rounded border cursor-pointer hover:bg-opacity-30 transition-colors">
                                      <FiFile className="text-current" />
                                      <div className="flex-1 min-w-0">
                                        <span className="text-xs truncate block">{file.name}</span>
                                        <span className="text-xs opacity-75">{formatFileSize(file.size)}</span>
                                      </div>
                                      <FiDownload className="text-current" size={14} />
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}

                          {/* Message footer */}
                          <div className="flex justify-between items-center mt-1">
                            <span className="text-xs opacity-75">
                              {dayjs(message.createdAt).format("h:mm A")}
                            </span>

                            {/* Message status for own messages */}
                            {isOwnMessage && (
                              <div className="flex items-center gap-1 ml-2">
                                {isPending ? (
                                  <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin"></div>
                                ) : message.readAt ? (
                                  <span className="text-xs opacity-75">✓✓</span>
                                ) : message.deliveredAt ? (
                                  <span className="text-xs opacity-75">✓</span>
                                ) : (
                                  <span className="text-xs opacity-75">⏳</span>
                                )}
                              </div>
                            )}
                          </div>

                          {/* Message actions (hover) */}
                          <div className="absolute -right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <button
                              className="p-1 bg-white shadow-md rounded-full text-gray-600 hover:text-gray-800"
                              onClick={() => setMessageToReply(message)}
                              title="Reply"
                            >
                              <FiMoreVertical size={14} />
                            </button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <FiMessageCircle size={48} className="text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Start a conversation</p>
              </div>
            </div>
          )}
        </div>

        {/* File previews before sending */}
        {filePreviews.length > 0 && (
          <div className="flex gap-2 mb-2 overflow-x-auto p-2 bg-gray-100 rounded">
            {filePreviews.map((preview, index) => (
              <div key={index} className="relative shrink-0">
                {preview.type === "image" ? (
                  <img
                    src={preview.preview}
                    alt="Preview"
                    className="h-20 w-20 object-cover rounded border"
                  />
                ) : (
                  <div className="h-20 w-20 bg-gray-200 rounded border flex flex-col items-center justify-center p-2">
                    <FiFile size={24} className="text-gray-500" />
                    <span className="text-xs text-center truncate w-full">
                      {preview.name}
                    </span>
                  </div>
                )}
                <button
                  onClick={() => removeFilePreview(index)}
                  className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow hover:bg-gray-100"
                >
                  <FiX size={14} className="text-gray-600" />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Enhanced typing indicator */}
        {showTypingIndicator && typing && (
          <div className="flex items-center gap-2 p-2 text-sm text-gray-500 mb-2">
            <div className="flex -space-x-1">
              <img
                src={otherUser?.image || otherUser?.profilePicture || userVector}
                alt={otherUser?.firstname || otherUser?.firstName}
                className="w-6 h-6 rounded-full border-2 border-white"
              />
            </div>
            <span>
              {otherUser?.firstname || otherUser?.firstName} is typing...
            </span>
            <div className="flex gap-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-100"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-200"></div>
            </div>
          </div>
        )}

        {/* Enhanced Message Input */}
        <div className="relative">
          {/* Hidden file input */}
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            multiple
          />

          {/* Textarea with send button */}
          <div className="relative bg-white p-2 rounded-md border">
            <textarea
              ref={textareaRef}
              value={chatMessage}
              onChange={handleTypingChange}
              onCompositionStart={() => setIsComposing(true)}
              onCompositionEnd={() => setIsComposing(false)}
              onKeyDown={handleKeyPress}
              placeholder="Type a message..."
              className="w-full rounded-md focus:outline-none resize-none"
              rows={2}
            />

            <div className="flex justify-between items-end">
              <button
                onClick={handleFileSelect}
                className="text-gray-500 hover:text-gray-700 p-1"
                title="Attach file"
              >
                <FiPaperclip size={20} />
              </button>

              <button
                onClick={sendChatMessage}
                disabled={
                  (!chatMessage.trim() && filePreviews.length === 0)
                }
                className={`flex gap-1 items-center px-3 py-2 rounded-md text-white ${
                  (chatMessage.trim() || filePreviews.length > 0)
                    ? "bg-primary"
                    : "bg-gray-300 cursor-not-allowed"
                }`}
                title="Send message"
              >
                Send
                <FiSend size={15} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatRoom;
